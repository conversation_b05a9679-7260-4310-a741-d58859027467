<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { addApi, updateApi } from '@/api/questionnaire';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => null
  },
  products: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单数据
const formData = ref({
  name: '',
  targetType: 'doctor',
  productCode: undefined,
  questions: [],
  scoreLevel: [
    {
      minScore: 0,
      maxScore: 0,
      label: ''
    }
  ]
});

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入问卷名称', trigger: 'blur' }],
  targetType: [{ required: true, message: '请选择对象类型', trigger: 'change' }],
  productCode: [{ required: true, message: '请选择关联产品', trigger: 'change' }],
  'scoreLevel.*.minScore': [{ required: true, message: '请输入最小分值', trigger: 'blur' }],
  'scoreLevel.*.maxScore': [{ required: true, message: '请输入最大分值', trigger: 'blur' }],
  'scoreLevel.*.label': [{ required: true, message: '请输入等级名称', trigger: 'blur' }]
};

// 目标类型选项
const targetTypeOptions = [
  { label: '医生', value: 'doctor' },
  { label: '机构', value: 'institution' },
  { label: '员工', value: 'employee' }
];

// 问题类型选项
const questionTypeOptions = [
  { label: '单选', value: 'single' },
  { label: '数值型', value: 'numeric' },
  { label: '文本型', value: 'text' }
];

// 添加问题
const addQuestion = () => {
  formData.value.questions.push({
    title: '',
    type: 'single',
    options: [
      { content: '', score: 0 }
    ]
  });
};

// 删除问题
const removeQuestion = (index) => {
  formData.value.questions.splice(index, 1);
};

// 添加选项
const addOption = (questionIndex) => {
  formData.value.questions[questionIndex].options.push({
    content: '',
    score: 0
  });
};

// 删除选项
const removeOption = (questionIndex, optionIndex) => {
  formData.value.questions[questionIndex].options.splice(optionIndex, 1);
};

// 添加分值范围
const addScoreRange = () => {
  formData.value.scoreLevel.push({
    minScore: 0,
    maxScore: 0,
    label: ''
  });
};

// 删除分值范围
const removeScoreRange = (index) => {
  formData.value.scoreLevel.splice(index, 1);
};

// 表单引用
const formRef = ref(null);

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    const api = formData.value?.id ? updateApi : addApi;
    await api({
      ...formData.value,
      status: '2',
      questions: JSON.stringify(formData.value.questions),
      scoreLevel: JSON.stringify(formData.value.scoreLevel)
    })

    ElMessage.success(`${props.editData ? '编辑' : '创建'}成功`);
    emit('success');
    handleClose();
  } catch (error) {
    if (error?.message) {
      ElMessage.error(error.message);
    }
  }
};

const handleSelectProduct = item => {
  formData.value.productName = item.name
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
  formData.value = {
    name: '',
    targetType: 'doctor',
    productCode: undefined,
    questions: [],
    scoreLevel: [
      {
        minScore: 0,
        maxScore: 0,
        label: ''
      }
    ]
  };
};

// 监听编辑数据变化
watch(() => props.editData, (val) => {
  if (val) {
    const data = JSON.parse(JSON.stringify(val));
    // 确保每个问题都有类型字段
    if (data.questions) {
      data.questions = data.questions.map(question => {
        if (!question.type) {
          // 如果没有类型字段，根据是否有选项来判断类型
          question.type = question.options && question.options.length > 0 ? 'single' : 'text';
        }
        return question;
      });
    }
    formData.value = data;
  }
}, { immediate: true });

// 监听弹窗显示
watch(() => props.visible, (val) => {
  if(val) { /* empty */ }

});
</script>

<template>
  <el-dialog
    :title="readonly ? '查看问卷' : editData ? '编辑问卷' : '新增问卷'"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    width="1000px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" :disabled="readonly">
      <el-form-item label="问卷名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入问卷名称" />
      </el-form-item>

      <el-form-item label="对象类型" prop="targetType">
        <el-select v-model="formData.targetType" placeholder="请选择对象类型">
          <el-option v-for="item in targetTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联产品" prop="productCode">
        <el-select v-model="formData.productCode" placeholder="请选择关联产品">
          <el-option v-for="item in products" :key="item.code" :label="item.name" :value="item.code" @click="handleSelectProduct(item)" />
        </el-select>
      </el-form-item>

      <div class="questions-container">
        <div class="questions-header">
          <span class="questions-title">问题列表</span>
          <el-button type="primary" link @click="addQuestion">
            <el-icon><Plus /></el-icon>
            添加问题
          </el-button>
        </div>

        <div v-for="(question, questionIndex) in formData.questions" :key="questionIndex" class="question-item">
          <div class="question-header">
            <span class="question-index">Q{{ questionIndex + 1 }}</span>
            <el-button type="danger" link @click="removeQuestion(questionIndex)"> 删除问题 </el-button>
          </div>

          <el-form-item :prop="'questions.' + questionIndex + '.title'" :rules="{ required: true, message: '请输入问题标题', trigger: 'blur' }">
            <el-input v-model="question.title" placeholder="请输入问题标题" />
          </el-form-item>

          <el-form-item :prop="'questions.' + questionIndex + '.type'" :rules="{ required: true, message: '请选择问题类型', trigger: 'change' }">
            <el-select v-model="question.type" placeholder="请选择问题类型" style="width: 200px">
              <el-option v-for="item in questionTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 单选类型显示选项 -->
          <div v-if="question.type === 'single'" class="options-container">
            <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-item">
              <el-form-item
                :prop="'questions.' + questionIndex + '.options.' + optionIndex + '.content'"
                :rules="{ required: true, message: '请输入选项内容', trigger: 'blur' }"
              >
                <el-input v-model="option.content" placeholder="请输入选项内容">
                  <template #append>
                    <el-input-number v-model="option.score" :min="0" :max="100" placeholder="分值" style="width: 100px" />
                  </template>
                </el-input>
              </el-form-item>

              <el-button v-if="question.options.length > 1" type="danger" link @click="removeOption(questionIndex, optionIndex)"> 删除 </el-button>
            </div>

            <el-button type="primary" link @click="addOption(questionIndex)">
              <el-icon><Plus /></el-icon>
              添加选项
            </el-button>
          </div>

          <!-- 数值型问题 -->
          <div v-if="question.type === 'numeric'" class="numeric-container">
            <el-form-item
              label-position="top"
              :prop="'questions.' + questionIndex + '.score'"
              :rules="{ required: true, message: '请输入分数', trigger: 'blur' }"
            >
              <el-input-number v-model="question.score" :min="0" :controls="false" placeholder="分数" style="width: 80px" />
            </el-form-item>
            <el-text type="info" size="small" style="padding-bottom: 4px; display: inline-block;">可设置填写答案分数，如无设置0</el-text>
            <div class="numeric-hint">
              <el-text type="info">数值型问题，用户将输入一个数字</el-text>
            </div>
          </div>

          <!-- 文本型问题 -->
          <div v-if="question.type === 'text'" class="text-container">
            <el-form-item
              label-position="top"
              :prop="'questions.' + questionIndex + '.score'"
              :rules="{ required: true, message: '请输入分数', trigger: 'blur' }"
            >
              <el-input-number v-model="question.score" :min="0" :controls="false" placeholder="分数" style="width: 80px" />
            </el-form-item>
            <el-text type="info" size="small" style="padding-bottom: 4px; display: inline-block;">可设置填写答案分数，如无设置0</el-text>
            <div class="text-hint">
              <el-text type="info">文本型问题，用户可自由输入文本内容</el-text>
            </div>
          </div>
        </div>
      </div>

      <!-- 总分分值等级设置 -->
      <div class="score-ranges-container">
        <div class="score-ranges-header">
          <span class="score-ranges-title">总分分值等级设置</span>
          <el-button type="primary" link @click="addScoreRange">
            <el-icon><Plus /></el-icon>
            添加分值范围
          </el-button>
        </div>

        <div class="score-ranges-list">
          <div v-for="(range, index) in formData.scoreLevel" :key="index" class="score-range-item">
            <div class="score-range-inputs">
              <el-form-item :prop="'scoreLevel.' + index + '.minScore'" :rules="rules['scoreLevel.*.minScore']" class="score-input">
                <template #label>
                  <span class="score-label">最小分值</span>
                </template>
                <el-input-number v-model="range.minScore" :min="0" :max="range.maxScore" :controls="false" class="score-number-input" />
              </el-form-item>

              <span class="score-range-separator">-</span>

              <el-form-item :prop="'scoreLevel.' + index + '.maxScore'" :rules="rules['scoreLevel.*.maxScore']" class="score-input">
                <template #label>
                  <span class="score-label">最大分值</span>
                </template>
                <el-input-number v-model="range.maxScore" :min="range.minScore" :controls="false" class="score-number-input" />
              </el-form-item>

              <el-form-item :prop="'scoreLevel.' + index + '.label'" :rules="rules['scoreLevel.*.label']" class="score-input">
                <template #label>
                  <span class="score-label">等级名称</span>
                </template>
                <el-input v-model="range.label" placeholder="请输入等级名称" class="label-input" />
              </el-form-item>
            </div>

            <div class="score-range-actions">
              <el-button v-if="formData.scoreLevel.length > 1" type="danger" link @click="removeScoreRange(index)"> 删除 </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">{{ readonly ? '关闭' : '取消' }}</el-button>
      <el-button v-if="!readonly" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.score-ranges-container {
  margin-bottom: 24px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #fafafa;
}

.score-ranges-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.score-ranges-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.score-ranges-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.score-range-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
}

.score-range-inputs {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-input {
  margin-bottom: 0;
}

.score-label {
  font-size: 14px;
  color: #606266;
}

.score-number-input {
  width: 100px;
}

.score-range-separator {
  color: #909399;
  font-weight: bold;
}

.label-input {
  width: 200px;
}

.score-range-actions {
  display: flex;
  align-items: center;
}

.questions-container {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.questions-title {
  font-size: 16px;
  font-weight: bold;
}

.question-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-index {
  font-size: 16px;
  font-weight: bold;
}

.options-container {
  margin-left: 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.numeric-container {
  margin-left: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.numeric-hint {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 8px;
}

.numeric-hint::before {
  content: "🔢";
  font-size: 16px;
}

.text-container {
  margin-left: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.text-hint {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-hint::before {
  content: "💡";
  font-size: 16px;
}
</style>
