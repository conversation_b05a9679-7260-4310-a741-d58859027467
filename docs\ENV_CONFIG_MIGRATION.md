# 环境变量统一管理迁移文档

## 概述

为了统一管理不同环境下的应用配置，我们引入了基于 `isAis` 判断的环境变量管理机制。通过检测 `window.location.hostname` 是否包含 "athena-ais" 来自动选择对应的环境变量。

## 变更内容

### 1. 新增工具文件

- `src/utils/envConfig.ts` - 主应用环境变量管理工具
- `src/msr-temp/src/utils/envConfig.ts` - MSR 应用环境变量管理工具

### 2. 环境变量映射关系

| 统一变量名   | 非 AIS 环境           | AIS 环境                 |
| ------------ | --------------------- | ------------------------ |
| APP_ID       | VITE_APP_ID           | VITE_APP_048_ID          |
| APPID_ID     | VITE_APP_APPID_ID     | VITE_APP_APPID048_ID     |
| REDIRECT_URL | VITE_APP_REDIRECT_URL | VITE_APP_REDIRECT048_URL |

### 3. 判断逻辑

```typescript
const isAis = window.location.hostname.includes("athena-ais");
```

- 当域名包含 "athena-ais" 时，使用 048 系列变量
- 否则使用普通变量

### 4. 提供的工具函数

```typescript
// 获取 APP ID
export const getAppId = () => string

// 获取 APPID ID (小程序appid)
export const getAppIdId = () => string

// 获取重定向地址
export const getRedirectUrl = () => string

// 获取完整配置对象
export const getEnvConfig = () => object

// 导出判断变量
export { isAis }
```

## 使用方法

### 旧的使用方式

```typescript
// 之前的代码
window.location.href = `https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=${import.meta.env.VITE_APP_APPID_ID}&redirect_uri=${import.meta.env.VITE_APP_REDIRECT_URL}/applet_login/&scope=&state=`;
```

### 新的使用方式

```typescript
// 导入工具函数
import { getAppIdId, getRedirectUrl } from '@/utils/envConfig';

// 使用工具函数
window.location.href = `https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=${getAppIdId()}&redirect_uri=${getRedirectUrl()}/applet_login/&scope=&state=`;
```

## 已更新的文件

1. **环境变量配置文件**

   - `.env.development` - 开发环境配置
   - `.env.production` - 生产环境配置
   - `src/msr-temp/.env.development` - MSR 开发环境配置
   - `src/msr-temp/.env.production` - MSR 生产环境配置

2. **TypeScript 类型定义**

   - `src/types/env.d.ts` - 主应用类型定义
   - `src/msr-temp/src/types/env.d.ts` - MSR 应用类型定义

3. **业务代码**
   - `src/views/login.vue` - 主应用登录页面
   - `src/msr-temp/src/views/login.vue` - MSR 应用登录页面

## 环境变量配置

确保在所有环境配置文件中都包含以下变量：

```bash
# 普通环境变量
VITE_APP_ID = 'cli_xxxxxxxxx'
VITE_APP_APPID_ID = 'cli_xxxxxxxxx'
VITE_APP_REDIRECT_URL = 'https://athena-ade.dgtmeta.com'

# AIS 环境变量
VITE_APP_048_ID = 'cli_xxxxxxxxx'
VITE_APP_APPID048_ID = 'cli_xxxxxxxxx'
VITE_APP_REDIRECT048_URL = 'https://athena-ais.dgtmeta.com'
```

## 测试

运行测试以验证配置正确性：

```bash
npm test src/utils/envConfig.test.ts
```

## 注意事项

1. 确保所有环境配置文件都包含完整的变量定义
2. 新增使用这些变量的代码时，请使用工具函数而不是直接访问 `import.meta.env`
3. 如果需要添加新的环境相关变量，请按照相同的模式进行扩展

## 迁移检查清单

- [x] 创建环境变量管理工具
- [x] 更新 TypeScript 类型定义
- [x] 更新登录页面代码
- [x] 确保所有环境配置文件包含必要变量
- [x] 创建测试文件
- [x] 创建迁移文档

## 后续计划

1. 监控生产环境运行情况
2. 根据需要扩展更多环境相关变量的管理
3. 考虑将此模式应用到其他配置项
