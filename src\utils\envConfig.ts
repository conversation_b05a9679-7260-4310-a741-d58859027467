/**
 * 环境变量统一管理工具
 * 根据 isAis 来区分使用不同的环境变量
 */

// 判断是否为 AIS 环境
const isAis = window.location.hostname.includes('athena-ais');

/**
 * 获取统一的环境变量配置
 */
export const getEnvConfig = () => {
  return {
    // APP ID
    VITE_APP_ID: isAis ? import.meta.env.VITE_APP_048_ID : import.meta.env.VITE_APP_ID,

    // APPID ID (小程序appid)
    VITE_APP_APPID_ID: isAis ? import.meta.env.VITE_APP_APPID048_ID : import.meta.env.VITE_APP_APPID_ID,

    // 重定向地址
    VITE_APP_REDIRECT_URL: isAis ? import.meta.env.VITE_APP_REDIRECT048_URL : import.meta.env.VITE_APP_REDIRECT_URL
  };
};

/**
 * 获取 APP ID
 */
export const getAppId = () => {
  return isAis ? import.meta.env.VITE_APP_048_ID : import.meta.env.VITE_APP_ID;
};

/**
 * 获取 APPID ID (小程序appid)
 */
export const getAppIdId = () => {
  return isAis ? import.meta.env.VITE_APP_APPID048_ID : import.meta.env.VITE_APP_APPID_ID;
};

/**
 * 获取重定向地址
 */
export const getRedirectUrl = () => {
  return isAis ? import.meta.env.VITE_APP_REDIRECT048_URL : import.meta.env.VITE_APP_REDIRECT_URL;
};

/**
 * 导出 isAis 判断变量
 */
export { isAis };
