/**
 * 环境变量配置测试文件
 * 用于验证 envConfig 工具的正确性
 */

import { getAppId, getAppIdId, getRedirectUrl, getEnvConfig, isAis } from './envConfig';

// 模拟不同的 hostname 环境
const originalLocation = window.location;

describe('envConfig', () => {
  beforeEach(() => {
    // 重置 window.location
    delete (window as any).location;
  });

  afterEach(() => {
    // 恢复原始的 window.location
    window.location = originalLocation;
  });

  test('should return normal env vars when hostname does not include athena-ais', () => {
    // 模拟非 AIS 环境
    (window as any).location = {
      hostname: 'athena-ade.dgtmeta.com'
    };

    // 模拟环境变量
    const mockEnv = {
      VITE_APP_ID: 'normal_app_id',
      VITE_APP_048_ID: 'ais_app_id',
      VITE_APP_APPID_ID: 'normal_appid',
      VITE_APP_APPID048_ID: 'ais_appid',
      VITE_APP_REDIRECT_URL: 'https://athena-ade.dgtmeta.com',
      VITE_APP_REDIRECT048_URL: 'https://athena-ais.dgtmeta.com'
    };

    // 模拟 import.meta.env
    Object.defineProperty(import.meta, 'env', {
      value: mockEnv,
      configurable: true
    });

    expect(getAppId()).toBe('normal_app_id');
    expect(getAppIdId()).toBe('normal_appid');
    expect(getRedirectUrl()).toBe('https://athena-ade.dgtmeta.com');
  });

  test('should return AIS env vars when hostname includes athena-ais', () => {
    // 模拟 AIS 环境
    (window as any).location = {
      hostname: 'athena-ais.dgtmeta.com'
    };

    // 模拟环境变量
    const mockEnv = {
      VITE_APP_ID: 'normal_app_id',
      VITE_APP_048_ID: 'ais_app_id',
      VITE_APP_APPID_ID: 'normal_appid',
      VITE_APP_APPID048_ID: 'ais_appid',
      VITE_APP_REDIRECT_URL: 'https://athena-ade.dgtmeta.com',
      VITE_APP_REDIRECT048_URL: 'https://athena-ais.dgtmeta.com'
    };

    // 模拟 import.meta.env
    Object.defineProperty(import.meta, 'env', {
      value: mockEnv,
      configurable: true
    });

    expect(getAppId()).toBe('ais_app_id');
    expect(getAppIdId()).toBe('ais_appid');
    expect(getRedirectUrl()).toBe('https://athena-ais.dgtmeta.com');
  });

  test('getEnvConfig should return complete config object', () => {
    const config = getEnvConfig();

    expect(config).toHaveProperty('VITE_APP_ID');
    expect(config).toHaveProperty('VITE_APP_APPID_ID');
    expect(config).toHaveProperty('VITE_APP_REDIRECT_URL');
  });
});
