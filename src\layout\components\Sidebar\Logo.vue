<template>
  <div
    class="sidebar-logo-container"
    :class="{ 'collapse': collapse }"
    :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }"
  >
    <transition :enter-active-class="proxy?.animate.logoAnimate.enter" mode="out-in">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logoClose" :src="isAis ? logo048 :logoClose" class="sidebar-logo-close" />
        <!-- <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">
          {{ title }}
        </h1> -->
      </router-link>

      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="isAis" :src="logo048" class="sidebar-logo-048" />
        <img v-else :src="logo" class="sidebar-logo" />
        <h1
          v-if="isAis"
          class="sidebar-title"
          :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }"
        >
          免疫事业部CRM
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup lang="ts">
import variables from '@/assets/styles/variables.module.scss';
import logo from '@/assets/logo/logo.png';
import logo048 from '@/assets/logo/048logo.jpg';
import logoClose from '@/assets/logo/logoClose.png';
import useSettingsStore from '@/store/modules/settings';
import { ComponentInternalInstance } from 'vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import{ isAis } from '@/utils/envConfig';

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
});

const title = ref('Athena');
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  border-bottom: 1px solid #f4f5f7;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    text-align: center;
    display: flex !important;
    align-items: center;

    & .sidebar-logo {
      width: 213px;
      vertical-align: middle;
      margin-left: 20px;
    }
    & .sidebar-logo-048 {
      width: 60px;
      margin-left: 20px;
    }
    & .sidebar-logo-close {
      width: 30px;
      height: 30px;
      vertical-align: middle;
    }
    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #333 !important;
      font-weight: 600;
      line-height: 50px;
      font-size: 18px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
      width: 30px;
      height: 30px;
    }
  }
}
</style>
