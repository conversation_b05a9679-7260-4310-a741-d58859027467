/**
 * 验证环境变量配置的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要检查的环境变量
const requiredEnvVars = [
  'VITE_APP_ID',
  'VITE_APP_048_ID',
  'VITE_APP_APPID_ID',
  'VITE_APP_APPID048_ID',
  'VITE_APP_REDIRECT_URL',
  'VITE_APP_REDIRECT048_URL'
];

// 需要检查的环境文件
const envFiles = ['.env.development', '.env.production', 'src/msr-temp/.env.development', 'src/msr-temp/.env.production'];

console.log('🔍 开始验证环境变量配置...\n');

let hasErrors = false;

envFiles.forEach((filePath) => {
  console.log(`📁 检查文件: ${filePath}`);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(filePath, 'utf-8');
  const missingVars = [];

  requiredEnvVars.forEach((varName) => {
    const regex = new RegExp(`^${varName}\\s*=`, 'm');
    if (!regex.test(content)) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.log(`❌ 缺少环境变量: ${missingVars.join(', ')}`);
    hasErrors = true;
  } else {
    console.log('✅ 所有必需的环境变量都存在');
  }

  console.log('');
});

// 检查工具文件是否存在
const utilFiles = ['src/utils/envConfig.ts', 'src/msr-temp/src/utils/envConfig.ts'];

console.log('🔧 检查工具文件...\n');

utilFiles.forEach((filePath) => {
  console.log(`📁 检查文件: ${filePath}`);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    hasErrors = true;
  } else {
    console.log('✅ 工具文件存在');
  }

  console.log('');
});

// 检查类型定义文件
const typeFiles = ['src/types/env.d.ts', 'src/msr-temp/src/types/env.d.ts'];

console.log('📝 检查类型定义文件...\n');

typeFiles.forEach((filePath) => {
  console.log(`📁 检查文件: ${filePath}`);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    hasErrors = true;
  } else {
    const content = fs.readFileSync(filePath, 'utf-8');
    const hasAllTypes = requiredEnvVars.every((varName) => content.includes(`${varName}: string`));

    if (hasAllTypes) {
      console.log('✅ 类型定义完整');
    } else {
      console.log('❌ 类型定义不完整');
      hasErrors = true;
    }
  }

  console.log('');
});

if (hasErrors) {
  console.log('❌ 验证失败，请检查上述错误');
  process.exit(1);
} else {
  console.log('🎉 所有配置验证通过！');
  process.exit(0);
}
