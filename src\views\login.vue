<template>
  <div class="skeleton" v-if="feishuLoading">
    <div id="loader"></div>
    <div class="skeleton-item" v-for="item in 9" :class="{'first':item===1}" :key="item"></div>
  </div>
  <div class="login" v-else>
    <div class="login-content">
      <img class="login-logo" :src="logo" alt="" v-if="!isAis" />
      <div v-else style="display: flex; align-items: center; gap: 10px">
        <img class="login-logo-048" :src="logo048" alt="" />
        <h1 class="title-048">免疫事业部CRM</h1>
      </div>
      <!-- <div v-if="!isShowfeishu"> -->
      <el-button v-if="!isShowfeishu" class="feishu-btn" type="primary" @click=" doFeishuLogin()">
        <img class="login-icon" :src="iconBtn" alt="" />
        飞书用户一键登录
      </el-button>
      <el-button v-if="!isShowfeishu" class="" type="text" text="plain" @click="() => isShowfeishu = true"> 账号登录 </el-button>
      <!-- </div> -->
      <el-form v-if="isShowfeishu" ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form-new">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
            <!-- <input :value="loginForm.username" /> -->
            <!-- <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template> -->
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
            <!-- <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template> -->
          </el-input>
        </el-form-item>
        <el-button :loading="loading" size="large" type="primary" style="width:100% ;margin-top:40px; color: #FFFFFF;" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div class="banck-feishu-btn" @click="doFeishuLogin()">
          <span>飞书登录</span>
        </div>
      </el-form>
    </div>

    <!-- <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form"> -->
    <!-- <h3 class="title">肿瘤CRM</h3>
      <el-form-item prop="tenantId" v-if="tenantEnabled">
        <el-select v-model="loginForm.tenantId" filterable placeholder="请选择/输入公司名称" style="width: 100%">
          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"></el-option>
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
      </el-form-item>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
          <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox> -->
    <!--      <el-form-item style="float: right;">-->
    <!--        <el-button circle title="微信登录" @click="doSocialLogin('wechat')">-->
    <!--          <svg-icon icon-class="wechat" />-->
    <!--        </el-button>-->
    <!--        <el-button circle title="MaxKey登录" @click="doSocialLogin('maxkey')">-->
    <!--          <svg-icon icon-class="maxkey" />-->
    <!--        </el-button>-->
    <!--        <el-button circle title="Gitee登录" @click="doSocialLogin('gitee')">-->
    <!--          <svg-icon icon-class="gitee" />-->
    <!--        </el-button>-->
    <!--        <el-button circle title="Github登录" @click="doSocialLogin('github')">-->
    <!--          <svg-icon icon-class="github" />-->
    <!--        </el-button>-->
    <!--      </el-form-item>-->
    <!-- <el-form-item style="width:100%;">
        <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item> -->
    <!-- </el-form> -->
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>Copyrigvht © 2018-2023 疯狂的狮子Li All Rights Reserved.</span>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getFeishuCode, callback } from '#/api/login';
import { authBinding } from '#/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '#/api/types';
import { getAppId, getAppIdId, getRedirectUrl, isAis } from '@/utils/envConfig';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import logo from '@/assets/images/login/logo.png';
import logo048 from '@/assets/logo/048logo.jpg';
import iconBtn from '@/assets/images/login/feishu.png';
const userStore = useUserStore();
const router = useRouter();
const isShowfeishu = ref(false);
const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);

const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: '请输入您的租户编号' }],
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
};

const codeUrl = ref('');
const loading = ref(false);
const feishuLoading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 租户开关
const tenantEnabled = ref(true);

// 注册开关
const register = ref(false);
const redirect = ref(undefined);
const paramsObject = ref({});
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    console.log(newRoute.query, 'newRoute.query');
    const url = (newRoute.query && newRoute.query.redirect) || '';
    const urlParams = new URLSearchParams(url.substring(url.indexOf('?')));
    const _paramsObject = {};

    for (const [key, value] of urlParams) {
      _paramsObject[key] = value;
    }
    paramsObject.value = _paramsObject;
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        await router.push({ path: redirect.value || '/', query: paramsObject.value });
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        // if (captchaEnabled.value) {
        //   await getCode();
        // }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {

};

//检测租户选择框的变化
watch(
  () => loginForm.value.tenantId,
  () => {
    localStorage.setItem('tenantId', String(loginForm.value.tenantId));
  }
);

// 飞书登录
const doFeishuLogin = async () => {
  const res = await getFeishuCode();
  // window.location.href= res.data.authorizeUrl
  window.location.href = `https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=${getAppIdId()}&redirect_uri=${
    getRedirectUrl()
  }/applet_login/&scope=&state=`;
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 第三方登录的callback
const getCallback = async (data: any) => {
  // const res = await callback(data )
  // console.log(res,'res');
  // 调用action的登录方法
  const [err] = await to(userStore.feishuLogin(data));
  if (!err) {
    await router.push({ path: redirect.value || '/', query: paramsObject.value });
    loading.value = false;
  } else {
    loading.value = false;
    // // 重新获取验证码
    // if (captchaEnabled.value) {
    //   await getCode();
    // }
  }
};

onBeforeMount(() => {
  const query = router.currentRoute.value.query;

  if (query.code && query.source) {
    feishuLoading.value = true;
    getCallback({ source: 'feishu', appCode: import.meta.env.VITE_APP_CODE, socialState: query.state, socialCode: query.code });
  }
}),
  onMounted(() => {
    // getCode();
    // initTenantList();
    getLoginData();
  });
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  padding-left: 107px;
  height: 100%;
  background-image: url('../assets/images/login/loginBg.png');
  background-size: cover;
}

// .title {
//   margin: 0px auto 30px auto;
//   text-align: center;
//   color: #707070;
// }

// .login-form {
//   border-radius: 6px;
//   background: #ffffff;
//   width: 400px;
//   padding: 25px 25px 5px 25px;

//   .el-input {
//     height: 40px;

//     input {
//       height: 40px;
//     }
//   }

//   .input-icon {
//     height: 39px;
//     width: 14px;
//     margin-left: 0px;
//   }
// }

// .login-tip {
//   font-size: 13px;
//   text-align: center;
//   color: #bfbfbf;
// }

// .login-code {
//   width: 33%;
//   height: 40px;
//   float: right;

//   img {
//     cursor: pointer;
//     vertical-align: middle;
//   }
// }

// .el-login-footer {
//   height: 40px;
//   line-height: 40px;
//   position: fixed;
//   bottom: 0;
//   width: 100%;
//   text-align: center;
//   color: #fff;
//   font-family: Arial, serif;
//   font-size: 12px;
//   letter-spacing: 1px;
// }

// .login-code-img {
//   height: 40px;
//   padding-left: 12px;
// }

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // margin-top: 288px;
  padding: 24px 0;
  .login-logo {
    transform: translateX(8px);
    height: 40px;
    padding-left: 32px;
  }
  .login-logo-048 {
    height: 60px;
  }
  .title-048 {
      margin: 0;
      color: #333 !important;
      font-weight: 600;
      line-height: 50px;
      font-size: 24px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  }
  .feishu-btn {
    width: 327px;
    height: 46px;
    margin-top: 151px;
    margin-bottom: 20px;
    .login-icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }
}
.login-form-new {
  width: 375px;
  margin-top: 80px;
  padding: 0 24px;
  ::v-deep {
    .el-input__wrapper {
      // background:#FFFFFF !important;
      width: 275px !important;
      // color: #BFBFBF;
    }

    .el-input {
      --el-input-border: #ffffff !important;
    }
    .el-button {
      --el-button-hover-text-color: #2551f2;
      --el-button-border-color: #2551f2 !important;
      --el-button-hover-border-color: #2551f2;
    }
  }
  .banck-feishu-btn {
    width: 100%;
    margin: 20px 0 0 0;
    text-align: center;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    color: #2551f2;
    border-radius: 4px;
    border: 1px solid #2551f2;
    &:hover {
      color: #5174f5;
      border: 1px solid #5174f5;
    }
  }
}

.skeleton {
  padding: 16px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.skeleton-item {
  height: 30px;
  margin: 0 30px;
  margin-top: 30px;
  border-radius: 14px;
  background: #f3f4f5;
  background-size: 400% 100%;
  -webkit-transition: all 0.3s 1s ease-out;
  transition: all 0.3s 1s ease-out;
}
.skeleton-item:first-child {
  width: 30%;
}
.skeleton-item:last-child {
  width: 50%;
}
</style>
